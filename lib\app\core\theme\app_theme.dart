import 'package:flutter/material.dart';
import 'package:flex_color_scheme/flex_color_scheme.dart';
import 'package:google_fonts/google_fonts.dart';

// Theme-aware color system
class AppColors {
  // Static colors for const contexts
  static const Color textPrimary = Color(0xFF1A1A1A);
  static const Color textSecondary = Color(0xFF6B7280);
  static const Color success = Color(0xFF10B981);
  static const Color warning = Color(0xFFF59E0B);
  static const Color error = Color(0xFFEF4444);
  static const Color info = Color(0xFF3B82F6);
  static const Color textLight = Color(0xFF9CA3AF);

  // Private theme-aware colors
  static const Color _lightTextPrimary = Color(0xFF1A1A1A);
  static const Color _lightTextSecondary = Color(0xFF6B7280);
  static const Color _lightSuccess = Color(0xFF10B981);
  static const Color _lightWarning = Color(0xFFF59E0B);
  static const Color _lightError = Color(0xFFEF4444);
  static const Color _lightInfo = Color(0xFF3B82F6);

  static const Color _darkTextPrimary = Color(0xFFF9FAFB);
  static const Color _darkTextSecondary = Color(0xFF9CA3AF);
  static const Color _darkSuccess = Color(0xFF34D399);
  static const Color _darkWarning = Color(0xFFFBBF24);
  static const Color _darkError = Color(0xFFF87171);
  static const Color _darkInfo = Color(0xFF60A5FA);

  // Theme-aware methods for dynamic contexts
  static Color getTextPrimary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkTextPrimary
        : _lightTextPrimary;
  }

  static Color getTextSecondary(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkTextSecondary
        : _lightTextSecondary;
  }

  static Color getSuccess(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkSuccess
        : _lightSuccess;
  }

  static Color getWarning(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkWarning
        : _lightWarning;
  }

  static Color getError(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkError
        : _lightError;
  }

  static Color getInfo(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark
        ? _darkInfo
        : _lightInfo;
  }
}

// Aurora Gradient Theme Extension for vivid color schemes
@immutable
class AuroraGradientExtension extends ThemeExtension<AuroraGradientExtension> {
  const AuroraGradientExtension({
    required this.primaryAuroraGradient,
    required this.secondaryAuroraGradient,
    required this.backgroundAuroraGradient,
    required this.accentAuroraGradient,
    required this.surfaceAuroraGradient,
    this.glowColor,
    this.shimmerColors,
  });

  final List<Color> primaryAuroraGradient;
  final List<Color> secondaryAuroraGradient;
  final List<Color> backgroundAuroraGradient;
  final List<Color> accentAuroraGradient;
  final List<Color> surfaceAuroraGradient;
  final Color? glowColor;
  final List<Color>? shimmerColors;

  @override
  AuroraGradientExtension copyWith({
    List<Color>? primaryAuroraGradient,
    List<Color>? secondaryAuroraGradient,
    List<Color>? backgroundAuroraGradient,
    List<Color>? accentAuroraGradient,
    List<Color>? surfaceAuroraGradient,
    Color? glowColor,
    List<Color>? shimmerColors,
  }) {
    return AuroraGradientExtension(
      primaryAuroraGradient:
          primaryAuroraGradient ?? this.primaryAuroraGradient,
      secondaryAuroraGradient:
          secondaryAuroraGradient ?? this.secondaryAuroraGradient,
      backgroundAuroraGradient:
          backgroundAuroraGradient ?? this.backgroundAuroraGradient,
      accentAuroraGradient: accentAuroraGradient ?? this.accentAuroraGradient,
      surfaceAuroraGradient:
          surfaceAuroraGradient ?? this.surfaceAuroraGradient,
      glowColor: glowColor ?? this.glowColor,
      shimmerColors: shimmerColors ?? this.shimmerColors,
    );
  }

  @override
  AuroraGradientExtension lerp(
      ThemeExtension<AuroraGradientExtension>? other, double t) {
    if (other is! AuroraGradientExtension) {
      return this;
    }
    return AuroraGradientExtension(
      primaryAuroraGradient: <Color>[
        Color.lerp(
            primaryAuroraGradient[0], other.primaryAuroraGradient[0], t)!,
        Color.lerp(
            primaryAuroraGradient[1], other.primaryAuroraGradient[1], t)!,
        if (primaryAuroraGradient.length > 2)
          Color.lerp(
              primaryAuroraGradient[2],
              other.primaryAuroraGradient.length > 2
                  ? other.primaryAuroraGradient[2]
                  : primaryAuroraGradient[2],
              t)!,
      ],
      secondaryAuroraGradient: <Color>[
        Color.lerp(
            secondaryAuroraGradient[0], other.secondaryAuroraGradient[0], t)!,
        Color.lerp(
            secondaryAuroraGradient[1], other.secondaryAuroraGradient[1], t)!,
        if (secondaryAuroraGradient.length > 2)
          Color.lerp(
              secondaryAuroraGradient[2],
              other.secondaryAuroraGradient.length > 2
                  ? other.secondaryAuroraGradient[2]
                  : secondaryAuroraGradient[2],
              t)!,
      ],
      backgroundAuroraGradient: <Color>[
        Color.lerp(
            backgroundAuroraGradient[0], other.backgroundAuroraGradient[0], t)!,
        Color.lerp(
            backgroundAuroraGradient[1], other.backgroundAuroraGradient[1], t)!,
        if (backgroundAuroraGradient.length > 2)
          Color.lerp(
              backgroundAuroraGradient[2],
              other.backgroundAuroraGradient.length > 2
                  ? other.backgroundAuroraGradient[2]
                  : backgroundAuroraGradient[2],
              t)!,
      ],
      accentAuroraGradient: <Color>[
        Color.lerp(accentAuroraGradient[0], other.accentAuroraGradient[0], t)!,
        Color.lerp(accentAuroraGradient[1], other.accentAuroraGradient[1], t)!,
      ],
      surfaceAuroraGradient: <Color>[
        Color.lerp(
            surfaceAuroraGradient[0], other.surfaceAuroraGradient[0], t)!,
        Color.lerp(
            surfaceAuroraGradient[1], other.surfaceAuroraGradient[1], t)!,
      ],
      glowColor: Color.lerp(glowColor, other.glowColor, t),
      shimmerColors: shimmerColors,
    );
  }

  // Aurora Gradient Themes for different schemes
  static const AuroraGradientExtension mandyRedLight = AuroraGradientExtension(
    primaryAuroraGradient: <Color>[
      Color(0xFFFF6B9D),
      Color(0xFFFF8E9B),
      Color(0xFFFFA8A8)
    ],
    secondaryAuroraGradient: <Color>[Color(0xFFFFB4B4), Color(0xFFFFD6CC)],
    backgroundAuroraGradient: <Color>[
      Color(0xFFFFF5F5),
      Color(0xFFFFE8E8),
      Color(0xFFFFD6D6)
    ],
    accentAuroraGradient: <Color>[Color(0xFFFF9A9E), Color(0xFFFAD0C4)],
    surfaceAuroraGradient: <Color>[Color(0xFFFFFAFA), Color(0xFFFFF0F0)],
    glowColor: Color(0xFFFF6B9D),
    shimmerColors: <Color>[
      Color(0xFFFFE8E8),
      Color(0xFFFFD6D6),
      Color(0xFFFFB4B4)
    ],
  );

  static const AuroraGradientExtension mandyRedDark = AuroraGradientExtension(
    primaryAuroraGradient: <Color>[
      Color(0xFFFF6B9D),
      Color(0xFFFF4757),
      Color(0xFFE84393)
    ],
    secondaryAuroraGradient: <Color>[Color(0xFF6C5CE7), Color(0xFFA29BFE)],
    backgroundAuroraGradient: <Color>[
      Color(0xFF2D3436),
      Color(0xFF636E72),
      Color(0xFF74B9FF)
    ],
    accentAuroraGradient: <Color>[Color(0xFFFF7675), Color(0xFFE17055)],
    surfaceAuroraGradient: <Color>[Color(0xFF2D3436), Color(0xFF636E72)],
    glowColor: Color(0xFFFF6B9D),
    shimmerColors: <Color>[
      Color(0xFF6C5CE7),
      Color(0xFFA29BFE),
      Color(0xFF74B9FF)
    ],
  );
}

// Available Aurora Theme Schemes
enum AuroraThemeScheme {
  mandyRed,
  oceanBlue,
  forestGreen,
  sunsetOrange,
  purpleNight,
  cosmicPink,
}

class AppTheme {
  // Get theme data for specific Aurora scheme
  static ThemeData getTheme(AuroraThemeScheme scheme, bool isDark) {
    switch (scheme) {
      case AuroraThemeScheme.mandyRed:
        return _buildMandyRedTheme(isDark);
      case AuroraThemeScheme.oceanBlue:
        return _buildOceanBlueTheme(isDark);
      case AuroraThemeScheme.forestGreen:
        return _buildForestGreenTheme(isDark);
      case AuroraThemeScheme.sunsetOrange:
        return _buildSunsetOrangeTheme(isDark);
      case AuroraThemeScheme.purpleNight:
        return _buildPurpleNightTheme(isDark);
      case AuroraThemeScheme.cosmicPink:
        return _buildCosmicPinkTheme(isDark);
    }
  }

  // Mandy Red Theme (Default)
  static ThemeData _buildMandyRedTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.mandyRed,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.mandyRed,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      extensions: <ThemeExtension<dynamic>>[
        isDark
            ? AuroraGradientExtension.mandyRedDark
            : AuroraGradientExtension.mandyRedLight,
      ],
      // Enhanced text theme
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      // Enhanced component themes
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Ocean Blue Theme
  static ThemeData _buildOceanBlueTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.blue,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.blue,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Forest Green Theme
  static ThemeData _buildForestGreenTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.green,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.green,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Sunset Orange Theme
  static ThemeData _buildSunsetOrangeTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.amber,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.amber,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Purple Night Theme
  static ThemeData _buildPurpleNightTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.purpleBrown,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.purpleBrown,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Cosmic Pink Theme
  static ThemeData _buildCosmicPinkTheme(bool isDark) {
    final baseTheme = isDark
        ? FlexThemeData.dark(
            scheme: FlexScheme.sakura,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          )
        : FlexThemeData.light(
            scheme: FlexScheme.sakura,
            useMaterial3: true,
            swapLegacyOnMaterial3: true,
            fontFamily: GoogleFonts.inter().fontFamily,
          );

    return baseTheme.copyWith(
      textTheme: _buildTextTheme(baseTheme.textTheme, isDark),
      elevatedButtonTheme: _buildElevatedButtonTheme(baseTheme.colorScheme),
      outlinedButtonTheme: _buildOutlinedButtonTheme(baseTheme.colorScheme),
      textButtonTheme: _buildTextButtonTheme(baseTheme.colorScheme),
      inputDecorationTheme: _buildInputDecorationTheme(baseTheme.colorScheme),
      cardTheme: _buildCardTheme(baseTheme.colorScheme),
      appBarTheme: _buildAppBarTheme(baseTheme.colorScheme, isDark),
    );
  }

  // Default themes for backward compatibility
  static ThemeData get lightTheme {
    return getTheme(AuroraThemeScheme.mandyRed, false);
  }

  static ThemeData get darkTheme {
    return getTheme(AuroraThemeScheme.mandyRed, true);
  }

  // Get theme name for display
  static String getThemeName(AuroraThemeScheme scheme) {
    switch (scheme) {
      case AuroraThemeScheme.mandyRed:
        return 'Mandy Red';
      case AuroraThemeScheme.oceanBlue:
        return 'Ocean Blue';
      case AuroraThemeScheme.forestGreen:
        return 'Forest Green';
      case AuroraThemeScheme.sunsetOrange:
        return 'Sunset Orange';
      case AuroraThemeScheme.purpleNight:
        return 'Purple Night';
      case AuroraThemeScheme.cosmicPink:
        return 'Cosmic Pink';
    }
  }

  // Helper methods for building component themes
  static TextTheme _buildTextTheme(TextTheme base, bool isDark) {
    final textColor =
        isDark ? AppColors._darkTextPrimary : AppColors._lightTextPrimary;
    final secondaryTextColor =
        isDark ? AppColors._darkTextSecondary : AppColors._lightTextSecondary;

    return base.copyWith(
      displayLarge: base.displayLarge?.copyWith(color: textColor),
      displayMedium: base.displayMedium?.copyWith(color: textColor),
      displaySmall: base.displaySmall?.copyWith(color: textColor),
      headlineLarge: base.headlineLarge?.copyWith(color: textColor),
      headlineMedium: base.headlineMedium?.copyWith(color: textColor),
      headlineSmall: base.headlineSmall?.copyWith(color: textColor),
      titleLarge: base.titleLarge?.copyWith(color: textColor),
      titleMedium: base.titleMedium?.copyWith(color: textColor),
      titleSmall: base.titleSmall?.copyWith(color: textColor),
      bodyLarge: base.bodyLarge?.copyWith(color: textColor),
      bodyMedium: base.bodyMedium?.copyWith(color: secondaryTextColor),
      bodySmall: base.bodySmall?.copyWith(color: secondaryTextColor),
      labelLarge: base.labelLarge?.copyWith(color: textColor),
      labelMedium: base.labelMedium?.copyWith(color: secondaryTextColor),
      labelSmall: base.labelSmall?.copyWith(color: secondaryTextColor),
    );
  }

  static ElevatedButtonThemeData _buildElevatedButtonTheme(
      ColorScheme colorScheme) {
    return ElevatedButtonThemeData(
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        elevation: 0,
        shadowColor: Colors.transparent,
      ),
    );
  }

  static OutlinedButtonThemeData _buildOutlinedButtonTheme(
      ColorScheme colorScheme) {
    return OutlinedButtonThemeData(
      style: OutlinedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        side: BorderSide(color: colorScheme.outline),
      ),
    );
  }

  static TextButtonThemeData _buildTextButtonTheme(ColorScheme colorScheme) {
    return TextButtonThemeData(
      style: TextButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
    );
  }

  static InputDecorationTheme _buildInputDecorationTheme(
      ColorScheme colorScheme) {
    return InputDecorationTheme(
      filled: true,
      fillColor: colorScheme.surface,
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      enabledBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: colorScheme.outline),
      ),
      focusedBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: colorScheme.primary, width: 2),
      ),
      errorBorder: OutlineInputBorder(
        borderRadius: BorderRadius.circular(16),
        borderSide: BorderSide(color: colorScheme.error),
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
    );
  }

  static CardThemeData _buildCardTheme(ColorScheme colorScheme) {
    return CardThemeData(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      color: colorScheme.surface,
    );
  }

  static AppBarTheme _buildAppBarTheme(ColorScheme colorScheme, bool isDark) {
    return AppBarTheme(
      elevation: 0,
      backgroundColor: colorScheme.surface,
      foregroundColor:
          isDark ? AppColors._darkTextPrimary : AppColors._lightTextPrimary,
      centerTitle: true,
      titleTextStyle: GoogleFonts.inter(
        fontSize: 18,
        fontWeight: FontWeight.w600,
        color:
            isDark ? AppColors._darkTextPrimary : AppColors._lightTextPrimary,
      ),
    );
  }
}

// Extension to easily access Aurora gradients from theme
extension ThemeDataExtensions on ThemeData {
  AuroraGradientExtension get auroraGradients =>
      extension<AuroraGradientExtension>()!;
}
